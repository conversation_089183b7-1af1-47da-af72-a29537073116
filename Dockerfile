# 使用官方Go镜像作为基础镜像
FROM golang:1.24.2-bullseye

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    tzdata \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装Node.js (Playwright需要)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 更新和下载Go依赖
RUN go env -w GO111MODULE=on
RUN go env -w  GOPROXY=https://goproxy.cn,direct
RUN go mod tidy
RUN go mod download

# 安装Playwright
RUN go run github.com/playwright-community/playwright-go/cmd/playwright install chromium
RUN go run github.com/playwright-community/playwright-go/cmd/playwright install-deps chromium

# 构建应用
RUN go build -o webscan ./cmd/main.go

# 创建报告目录
RUN mkdir -p /app/report

# 设置环境变量
ENV PLAYWRIGHT_BROWSERS_PATH=/root/.cache/ms-playwright

# 默认命令显示帮助
CMD ["./webscan", "--help"]
