package core

import (
	"encoding/json"
	"fmt"
	"runtime"
	"sync"
	"time"
)

// ErrorCode 错误代码类型
type ErrorCode string

// 错误代码常量定义
const (
	// 系统级错误
	ErrCodeSystemFailure   ErrorCode = "SYSTEM_FAILURE"
	ErrCodeMemoryExhausted ErrorCode = "MEMORY_EXHAUSTED"
	ErrCodeTimeoutExceeded ErrorCode = "TIMEOUT_EXCEEDED"

	// 配置相关错误
	ErrCodeConfigLoadFailed ErrorCode = "CONFIG_LOAD_FAILED"
	ErrCodeConfigInvalid    ErrorCode = "CONFIG_INVALID"

	// 网络相关错误
	ErrCodeNetworkFailure   ErrorCode = "NETWORK_FAILURE"
	ErrCodeConnectionFailed ErrorCode = "CONNECTION_FAILED"
	ErrCodeRequestTimeout   ErrorCode = "REQUEST_TIMEOUT"

	// 数据库相关错误
	ErrCodeDBConnectionFailed  ErrorCode = "DB_CONNECTION_FAILED"
	ErrCodeDBQueryFailed       ErrorCode = "DB_QUERY_FAILED"
	ErrCodeDBTransactionFailed ErrorCode = "DB_TRANSACTION_FAILED"

	// 页面处理错误
	ErrCodePageLoadFailed   ErrorCode = "PAGE_LOAD_FAILED"
	ErrCodePageParseFailed  ErrorCode = "PAGE_PARSE_FAILED"
	ErrCodeResourceNotFound ErrorCode = "RESOURCE_NOT_FOUND"

	// 数据处理错误
	ErrCodeDataInvalid      ErrorCode = "DATA_INVALID"
	ErrCodeExtractionFailed ErrorCode = "EXTRACTION_FAILED"
	ErrCodeValidationFailed ErrorCode = "VALIDATION_FAILED"
)

// ErrorSeverity 错误严重程度
type ErrorSeverity string

const (
	SeverityLow      ErrorSeverity = "LOW"
	SeverityMedium   ErrorSeverity = "MEDIUM"
	SeverityHigh     ErrorSeverity = "HIGH"
	SeverityCritical ErrorSeverity = "CRITICAL"
)

// ScanError 统一错误结构
type ScanError struct {
	Code       ErrorCode              `json:"code"`                  // 错误代码
	Message    string                 `json:"message"`               // 错误消息
	URL        string                 `json:"url,omitempty"`         // 相关URL
	Module     string                 `json:"module"`                // 模块名称
	Function   string                 `json:"function"`              // 函数名称
	Line       int                    `json:"line"`                  // 行号
	Severity   ErrorSeverity          `json:"severity"`              // 严重程度
	Timestamp  time.Time              `json:"timestamp"`             // 时间戳
	Cause      error                  `json:"cause,omitempty"`       // 原始错误
	Context    map[string]interface{} `json:"context,omitempty"`     // 错误上下文信息
	StackTrace string                 `json:"stack_trace,omitempty"` // 堆栈跟踪
	RequestID  string                 `json:"request_id,omitempty"`  // 请求ID，用于追踪
}

// ErrorManager 统一错误管理器
type ErrorManager struct {
	collectors    map[string]*ErrorCollector // 错误收集器
	handlers      []ScanErrorHandler         // 错误处理器
	filters       []ErrorFilter              // 错误过滤器
	mutex         sync.RWMutex               // 读写锁
	maxErrors     int                        // 最大错误数量
	autoCleanup   bool                       // 自动清理
	cleanupTicker *time.Ticker               // 清理定时器
	stopCh        chan bool                  // 停止信号
}

// ScanErrorHandler 错误处理器接口
type ScanErrorHandler interface {
	HandleError(err *ScanError)
	CanHandle(err *ScanError) bool
}

// ErrorFilter 错误过滤器接口
type ErrorFilter interface {
	ShouldFilter(err *ScanError) bool
}

// ErrorCollector 错误收集器
type ErrorCollector struct {
	errors   []ScanError
	mutex    sync.RWMutex
	maxSize  int
	category string
}

// 全局错误管理器实例
var (
	globalErrorManager *ErrorManager
	errorOnce          sync.Once
)

// GetErrorManager 获取全局错误管理器实例
func GetErrorManager() *ErrorManager {
	errorOnce.Do(func() {
		globalErrorManager = NewErrorManager()
	})
	return globalErrorManager
}

// NewErrorManager 创建新的错误管理器
func NewErrorManager() *ErrorManager {
	em := &ErrorManager{
		collectors: make(map[string]*ErrorCollector),
		handlers:   make([]ScanErrorHandler, 0),
		filters:    make([]ErrorFilter, 0),
		maxErrors:  10000,
		stopCh:     make(chan bool),
	}

	// 创建默认收集器
	em.CreateCollector("default", 1000)
	em.CreateCollector("critical", 500)
	em.CreateCollector("network", 500)
	em.CreateCollector("database", 500)

	return em
}

// === 错误创建方法 ===

// NewError 创建新的扫描错误
func (em *ErrorManager) NewError(code ErrorCode, message, url, module string, cause error) *ScanError {
	// 获取调用者信息
	_, _, line, _ := runtime.Caller(1)
	function := getFunctionName()

	severity := em.getSeverityByCode(code)

	err := &ScanError{
		Code:      code,
		Message:   message,
		URL:       url,
		Module:    module,
		Function:  function,
		Line:      line,
		Severity:  severity,
		Timestamp: time.Now(),
		Cause:     cause,
		Context:   make(map[string]interface{}),
	}

	// 如果是严重错误，添加堆栈跟踪
	if severity == SeverityCritical || severity == SeverityHigh {
		err.StackTrace = getStackTrace()
	}

	return err
}

// NewErrorWithContext 创建带上下文的错误
func (em *ErrorManager) NewErrorWithContext(code ErrorCode, message, url, module string, cause error, context map[string]interface{}) *ScanError {
	err := em.NewError(code, message, url, module, cause)
	err.Context = context
	return err
}

// === 错误处理方法 ===

// HandleError 处理错误
func (em *ErrorManager) HandleError(err *ScanError) {
	// 应用过滤器
	for _, filter := range em.filters {
		if filter.ShouldFilter(err) {
			return
		}
	}

	// 收集错误
	em.collectError(err)

	// 处理错误
	for _, handler := range em.handlers {
		if handler.CanHandle(err) {
			go handler.HandleError(err)
		}
	}
}

// HandleFatalError 处理致命错误并终止程序
func (em *ErrorManager) HandleFatalError(message, module, function string) {
	// 创建致命错误
	fatalErr := &ScanError{
		Code:       ErrCodeSystemFailure,
		Message:    message,
		Module:     module,
		Function:   function,
		Severity:   SeverityCritical,
		Timestamp:  time.Now(),
		StackTrace: getStackTrace(),
		Context:    make(map[string]interface{}),
	}

	// 记录致命错误
	em.collectError(fatalErr)

	// 打印错误信息到标准错误输出
	fmt.Printf("❌ 致命错误: %s\n", message)
	fmt.Printf("📍 模块: %s\n", module)
	fmt.Printf("🔧 函数: %s\n", function)
	fmt.Printf("⏰ 时间: %s\n", fatalErr.Timestamp.Format("2006-01-02 15:04:05"))
	fmt.Println("🚫 程序即将退出...")

	// 终止程序
	panic(fmt.Sprintf("致命错误: %s (模块: %s, 函数: %s)", message, module, function))
}

// collectError 收集错误到相应的收集器
func (em *ErrorManager) collectError(err *ScanError) {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	// 根据错误类型选择收集器
	category := em.getCategoryByError(err)

	if collector, exists := em.collectors[category]; exists {
		collector.Collect(err)
	}

	// 同时添加到默认收集器
	if defaultCollector, exists := em.collectors["default"]; exists && category != "default" {
		defaultCollector.Collect(err)
	}
}

// getCategoryByError 根据错误获取分类
func (em *ErrorManager) getCategoryByError(err *ScanError) string {
	switch err.Severity {
	case SeverityCritical:
		return "critical"
	}

	switch err.Code {
	case ErrCodeNetworkFailure, ErrCodeConnectionFailed, ErrCodeRequestTimeout:
		return "network"
	case ErrCodeDBConnectionFailed, ErrCodeDBQueryFailed, ErrCodeDBTransactionFailed:
		return "database"
	default:
		return "default"
	}
}

// === 收集器管理方法 ===

// CreateCollector 创建错误收集器
func (em *ErrorManager) CreateCollector(category string, maxSize int) *ErrorCollector {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	collector := &ErrorCollector{
		errors:   make([]ScanError, 0),
		maxSize:  maxSize,
		category: category,
	}

	em.collectors[category] = collector
	return collector
}

// GetCollector 获取错误收集器
func (em *ErrorManager) GetCollector(category string) *ErrorCollector {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	return em.collectors[category]
}

// === 处理器和过滤器管理 ===

// RegisterHandler 注册错误处理器
func (em *ErrorManager) RegisterHandler(handler ScanErrorHandler) {
	em.handlers = append(em.handlers, handler)
}

// RegisterFilter 注册错误过滤器
func (em *ErrorManager) RegisterFilter(filter ErrorFilter) {
	em.filters = append(em.filters, filter)
}

// === 查询方法 ===

// GetErrors 获取指定分类的错误
func (em *ErrorManager) GetErrors(category string) []ScanError {
	if collector := em.GetCollector(category); collector != nil {
		return collector.GetErrors()
	}
	return nil
}

// GetErrorsByCode 根据错误代码获取错误
func (em *ErrorManager) GetErrorsByCode(category string, code ErrorCode) []ScanError {
	if collector := em.GetCollector(category); collector != nil {
		return collector.GetErrorsByCode(code)
	}
	return nil
}

// GetErrorStats 获取错误统计信息
func (em *ErrorManager) GetErrorStats() map[string]int {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	stats := make(map[string]int)
	for category, collector := range em.collectors {
		stats[category] = collector.GetCount()
	}

	return stats
}

// === 工具方法 ===

// getSeverityByCode 根据错误代码获取严重程度
func (em *ErrorManager) getSeverityByCode(code ErrorCode) ErrorSeverity {
	severityMap := map[ErrorCode]ErrorSeverity{
		// 严重错误
		ErrCodeSystemFailure:      SeverityCritical,
		ErrCodeMemoryExhausted:    SeverityCritical,
		ErrCodeDBConnectionFailed: SeverityCritical,

		// 高级错误
		ErrCodeTimeoutExceeded:     SeverityHigh,
		ErrCodeNetworkFailure:      SeverityHigh,
		ErrCodeConfigLoadFailed:    SeverityHigh,
		ErrCodeDBTransactionFailed: SeverityHigh,

		// 中级错误
		ErrCodeConnectionFailed: SeverityMedium,
		ErrCodePageLoadFailed:   SeverityMedium,
		ErrCodeConfigInvalid:    SeverityMedium,
		ErrCodeDBQueryFailed:    SeverityMedium,

		// 低级错误
		ErrCodeResourceNotFound: SeverityLow,
		ErrCodeDataInvalid:      SeverityLow,
		ErrCodeValidationFailed: SeverityLow,
		ErrCodeExtractionFailed: SeverityLow,
	}

	if severity, exists := severityMap[code]; exists {
		return severity
	}

	return SeverityMedium // 默认中级错误
}

// === ErrorCollector 方法 ===

// Collect 收集错误
func (c *ErrorCollector) Collect(err *ScanError) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 如果超过最大大小，删除最旧的错误
	if len(c.errors) >= c.maxSize {
		c.errors = c.errors[1:]
	}

	c.errors = append(c.errors, *err)
}

// GetErrors 获取所有错误
func (c *ErrorCollector) GetErrors() []ScanError {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 返回副本，避免并发修改
	result := make([]ScanError, len(c.errors))
	copy(result, c.errors)
	return result
}

// GetErrorsByCode 根据错误代码获取错误
func (c *ErrorCollector) GetErrorsByCode(code ErrorCode) []ScanError {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	var result []ScanError
	for _, err := range c.errors {
		if err.Code == code {
			result = append(result, err)
		}
	}
	return result
}

// GetCount 获取错误数量
func (c *ErrorCollector) GetCount() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return len(c.errors)
}

// Clear 清空错误
func (c *ErrorCollector) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.errors = make([]ScanError, 0)
}

// === ScanError 方法 ===

// Error 实现error接口
func (e *ScanError) Error() string {
	return fmt.Sprintf("[%s][%s] %s: %s (URL: %s, Module: %s, Function: %s:%d)",
		e.Severity, e.Code, e.Message, e.getCauseMessage(), e.URL, e.Module, e.Function, e.Line)
}

// getCauseMessage 获取原始错误消息
func (e *ScanError) getCauseMessage() string {
	if e.Cause != nil {
		return e.Cause.Error()
	}
	return "无原始错误"
}

// IsCritical 检查是否为严重错误
func (e *ScanError) IsCritical() bool {
	return e.Severity == SeverityCritical
}

// ToJSON 转换为JSON字符串
func (e *ScanError) ToJSON() string {
	data, _ := json.Marshal(e)
	return string(data)
}

// === 辅助函数 ===

// getFunctionName 获取函数名称
func getFunctionName() string {
	pc, _, _, ok := runtime.Caller(2)
	if !ok {
		return "unknown"
	}

	fn := runtime.FuncForPC(pc)
	if fn == nil {
		return "unknown"
	}

	return fn.Name()
}

// getStackTrace 获取堆栈跟踪
func getStackTrace() string {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	return string(buf[:n])
}
