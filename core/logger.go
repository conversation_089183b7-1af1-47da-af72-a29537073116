package core

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	LogLevelDebug LogLevel = iota
	LogLevelInfo
	LogLevelWarn
	LogLevelError
	LogLevelFatal
)

// LogLevel 字符串表示
var logLevelNames = map[LogLevel]string{
	LogLevelDebug: "DEBUG",
	LogLevelInfo:  "INFO",
	LogLevelWarn:  "WARN",
	LogLevelError: "ERROR",
	LogLevelFatal: "FATAL",
}

// Logger 统一日志管理器 - 整合所有日志功能
// 功能：分级日志、文件轮转、格式化输出、异步写入等
type Logger struct {
	level        LogLevel                  // 日志级别
	outputs      map[LogLevel]io.Writer    // 不同级别的输出目标
	formatters   map[LogLevel]LogFormatter // 不同级别的格式化器
	mutex        sync.RWMutex              // 读写锁
	asyncMode    bool                      // 异步模式
	logChan      chan LogEntry             // 异步日志通道
	stopCh       chan bool                 // 停止信号
	wg           sync.WaitGroup            // 等待组
	errorHandler func(error)               // 错误处理器
}

// LogEntry 日志条目
type LogEntry struct {
	Level     LogLevel
	Timestamp time.Time
	Module    string
	Message   string
	Fields    map[string]interface{}
}

// LogFormatter 日志格式化器接口
type LogFormatter interface {
	Format(entry LogEntry) string
}

// DefaultFormatter 默认格式化器
type DefaultFormatter struct{}

// JSONFormatter JSON格式化器
type JSONFormatter struct{}

// FileRotator 文件轮转器
type FileRotator struct {
	basePath    string
	maxSize     int64
	maxFiles    int
	currentFile *os.File
	currentSize int64
	mutex       sync.Mutex
}

// 全局日志管理器实例
var (
	globalLogger *Logger
	loggerOnce   sync.Once
)

// GetLogger 获取全局日志管理器实例
func GetLogger() *Logger {
	loggerOnce.Do(func() {
		globalLogger = NewLogger()
	})
	return globalLogger
}

// NewLogger 创建新的日志管理器
func NewLogger() *Logger {
	logger := &Logger{
		level:      LogLevelInfo,
		outputs:    make(map[LogLevel]io.Writer),
		formatters: make(map[LogLevel]LogFormatter),
		logChan:    make(chan LogEntry, 1000),
		stopCh:     make(chan bool),
	}

	// 设置默认输出和格式化器
	logger.SetOutput(LogLevelDebug, os.Stdout)
	logger.SetOutput(LogLevelInfo, os.Stdout)
	logger.SetOutput(LogLevelWarn, os.Stderr)
	logger.SetOutput(LogLevelError, os.Stderr)
	logger.SetOutput(LogLevelFatal, os.Stderr)

	defaultFormatter := &DefaultFormatter{}
	logger.SetFormatter(LogLevelDebug, defaultFormatter)
	logger.SetFormatter(LogLevelInfo, defaultFormatter)
	logger.SetFormatter(LogLevelWarn, defaultFormatter)
	logger.SetFormatter(LogLevelError, defaultFormatter)
	logger.SetFormatter(LogLevelFatal, defaultFormatter)

	return logger
}

// === 配置方法 ===

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level LogLevel) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	l.level = level
}

// SetOutput 设置指定级别的输出目标
func (l *Logger) SetOutput(level LogLevel, output io.Writer) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	l.outputs[level] = output
}

// SetFormatter 设置指定级别的格式化器
func (l *Logger) SetFormatter(level LogLevel, formatter LogFormatter) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	l.formatters[level] = formatter
}

// EnableAsyncMode 启用异步模式
func (l *Logger) EnableAsyncMode() {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	if l.asyncMode {
		return
	}

	l.asyncMode = true
	l.wg.Add(1)
	go l.asyncLogWorker()
}

// DisableAsyncMode 禁用异步模式
func (l *Logger) DisableAsyncMode() {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	if !l.asyncMode {
		return
	}

	l.asyncMode = false
	l.stopCh <- true
	l.wg.Wait()
}

// SetErrorHandler 设置错误处理器
func (l *Logger) SetErrorHandler(handler func(error)) {
	l.errorHandler = handler
}

// === 日志记录方法 ===

// Debug 记录调试日志
func (l *Logger) Debug(module, message string, fields ...map[string]interface{}) {
	l.log(LogLevelDebug, module, message, fields...)
}

// Info 记录信息日志
func (l *Logger) Info(module, message string, fields ...map[string]interface{}) {
	l.log(LogLevelInfo, module, message, fields...)
}

// Warn 记录警告日志
func (l *Logger) Warn(module, message string, fields ...map[string]interface{}) {
	l.log(LogLevelWarn, module, message, fields...)
}

// Error 记录错误日志
func (l *Logger) Error(module, message string, fields ...map[string]interface{}) {
	l.log(LogLevelError, module, message, fields...)
}

// Fatal 记录致命错误日志
func (l *Logger) Fatal(module, message string, fields ...map[string]interface{}) {
	l.log(LogLevelFatal, module, message, fields...)
	os.Exit(1)
}

// log 内部日志记录方法
func (l *Logger) log(level LogLevel, module, message string, fields ...map[string]interface{}) {
	l.mutex.RLock()
	currentLevel := l.level
	asyncMode := l.asyncMode
	l.mutex.RUnlock()

	// 检查日志级别
	if level < currentLevel {
		return
	}

	// 构建日志条目
	entry := LogEntry{
		Level:     level,
		Timestamp: time.Now(),
		Module:    module,
		Message:   message,
		Fields:    make(map[string]interface{}),
	}

	// 合并字段
	for _, fieldMap := range fields {
		for k, v := range fieldMap {
			entry.Fields[k] = v
		}
	}

	if asyncMode {
		// 异步模式：发送到通道
		select {
		case l.logChan <- entry:
		default:
			// 通道满了，直接写入（避免阻塞）
			l.writeLog(entry)
		}
	} else {
		// 同步模式：直接写入
		l.writeLog(entry)
	}
}

// writeLog 写入日志
func (l *Logger) writeLog(entry LogEntry) {
	l.mutex.RLock()
	output := l.outputs[entry.Level]
	formatter := l.formatters[entry.Level]
	l.mutex.RUnlock()

	if output == nil || formatter == nil {
		return
	}

	// 格式化并写入
	formatted := formatter.Format(entry)
	if _, err := output.Write([]byte(formatted + "\n")); err != nil {
		if l.errorHandler != nil {
			l.errorHandler(err)
		}
	}
}

// asyncLogWorker 异步日志工作器
func (l *Logger) asyncLogWorker() {
	defer l.wg.Done()

	for {
		select {
		case entry := <-l.logChan:
			l.writeLog(entry)
		case <-l.stopCh:
			// 处理剩余的日志
			for {
				select {
				case entry := <-l.logChan:
					l.writeLog(entry)
				default:
					return
				}
			}
		}
	}
}

// === 便捷方法 ===

// Debugf 格式化调试日志
func (l *Logger) Debugf(module, format string, args ...interface{}) {
	l.Debug(module, fmt.Sprintf(format, args...))
}

// Infof 格式化信息日志
func (l *Logger) Infof(module, format string, args ...interface{}) {
	l.Info(module, fmt.Sprintf(format, args...))
}

// Warnf 格式化警告日志
func (l *Logger) Warnf(module, format string, args ...interface{}) {
	l.Warn(module, fmt.Sprintf(format, args...))
}

// Errorf 格式化错误日志
func (l *Logger) Errorf(module, format string, args ...interface{}) {
	l.Error(module, fmt.Sprintf(format, args...))
}

// Fatalf 格式化致命错误日志
func (l *Logger) Fatalf(module, format string, args ...interface{}) {
	l.Fatal(module, fmt.Sprintf(format, args...))
}

// === 文件日志支持 ===

// SetFileOutput 设置文件输出
func (l *Logger) SetFileOutput(level LogLevel, filePath string) error {
	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return err
	}

	l.SetOutput(level, file)
	return nil
}

// SetRotatingFileOutput 设置轮转文件输出
func (l *Logger) SetRotatingFileOutput(level LogLevel, basePath string, maxSize int64, maxFiles int) error {
	rotator := &FileRotator{
		basePath: basePath,
		maxSize:  maxSize,
		maxFiles: maxFiles,
	}

	if err := rotator.init(); err != nil {
		return err
	}

	l.SetOutput(level, rotator)
	return nil
}

// Close 关闭日志管理器
func (l *Logger) Close() {
	if l.asyncMode {
		l.DisableAsyncMode()
	}

	// 关闭文件输出
	l.mutex.Lock()
	defer l.mutex.Unlock()

	for _, output := range l.outputs {
		if file, ok := output.(*os.File); ok && file != os.Stdout && file != os.Stderr {
			file.Close()
		}
	}
}

// === 格式化器实现 ===

// Format 默认格式化器实现
func (f *DefaultFormatter) Format(entry LogEntry) string {
	timestamp := entry.Timestamp.Format("2006-01-02 15:04:05")
	level := logLevelNames[entry.Level]

	message := fmt.Sprintf("[%s] [%s] [%s] %s", timestamp, level, entry.Module, entry.Message)

	// 添加字段信息
	if len(entry.Fields) > 0 {
		message += " |"
		for k, v := range entry.Fields {
			message += fmt.Sprintf(" %s=%v", k, v)
		}
	}

	return message
}

// Format JSON格式化器实现
func (f *JSONFormatter) Format(entry LogEntry) string {
	data := map[string]interface{}{
		"timestamp": entry.Timestamp.Format(time.RFC3339),
		"level":     logLevelNames[entry.Level],
		"module":    entry.Module,
		"message":   entry.Message,
	}

	// 添加字段
	for k, v := range entry.Fields {
		data[k] = v
	}

	// 简化的JSON序列化（生产环境应使用json包）
	return fmt.Sprintf(`{"timestamp":"%s","level":"%s","module":"%s","message":"%s"}`,
		data["timestamp"], data["level"], data["module"], data["message"])
}

// === 文件轮转器实现 ===

// init 初始化文件轮转器
func (r *FileRotator) init() error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// 创建目录
	dir := filepath.Dir(r.basePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// 打开当前文件
	return r.openCurrentFile()
}

// openCurrentFile 打开当前文件
func (r *FileRotator) openCurrentFile() error {
	if r.currentFile != nil {
		r.currentFile.Close()
	}

	file, err := os.OpenFile(r.basePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return err
	}

	// 获取文件大小
	info, err := file.Stat()
	if err != nil {
		file.Close()
		return err
	}

	r.currentFile = file
	r.currentSize = info.Size()

	return nil
}

// Write 实现io.Writer接口
func (r *FileRotator) Write(p []byte) (n int, err error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// 检查是否需要轮转
	if r.currentSize+int64(len(p)) > r.maxSize {
		if err := r.rotate(); err != nil {
			return 0, err
		}
	}

	// 写入数据
	n, err = r.currentFile.Write(p)
	r.currentSize += int64(n)

	return n, err
}

// rotate 轮转文件
func (r *FileRotator) rotate() error {
	// 关闭当前文件
	if r.currentFile != nil {
		r.currentFile.Close()
	}

	// 重命名文件
	timestamp := time.Now().Format("20060102-150405")
	rotatedPath := fmt.Sprintf("%s.%s", r.basePath, timestamp)

	if err := os.Rename(r.basePath, rotatedPath); err != nil {
		return err
	}

	// 清理旧文件
	r.cleanupOldFiles()

	// 打开新文件
	return r.openCurrentFile()
}

// cleanupOldFiles 清理旧文件
func (r *FileRotator) cleanupOldFiles() {
	// 简化实现：不进行文件清理
	// 生产环境应该实现文件数量限制
}
