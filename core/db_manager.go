package core

import (
	"database/sql"
	"fmt"
	"time"
	"web_scanner/config"
)

// DBManager 简化的数据库管理器 - 只保留核心连接管理功能
type DBManager struct {
	db            *sql.DB          // 数据库连接实例
	projectPrefix string           // 项目前缀
	config        *config.DBConfig // 数据库配置
	errorManager  *ErrorManager    // 错误管理器
}

// NewDBManager 创建新的数据库管理器
func NewDBManager() *DBManager {
	return &DBManager{
		errorManager: GetErrorManager(),
	}
}

// InitConnection 初始化数据库连接
func (dm *DBManager) InitConnection(dbConfig *config.DBConfig, projectPrefix string) error {
	dm.config = dbConfig
	dm.projectPrefix = projectPrefix

	var db *sql.DB
	var err error

	// 根据数据库类型选择不同的连接方式
	if dbConfig.Driver == "sqlite3" {
		// SQLite直接连接到数据库文件
		db, err = sql.Open(dbConfig.Driver, dbConfig.DBName)
		if err != nil {
			return dm.handleError(ErrCodeDBConnectionFailed, "SQLite数据库连接失败", "", err)
		}
	} else {
		// MySQL/PostgreSQL需要先连接服务器再创建数据库
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/",
			dbConfig.User, dbConfig.Password, dbConfig.Host, dbConfig.Port)

		db, err = sql.Open(dbConfig.Driver, dsn)
		if err != nil {
			return dm.handleError(ErrCodeDBConnectionFailed, "数据库连接失败", "", err)
		}

		// 测试连接
		if err := db.Ping(); err != nil {
			db.Close()
			return dm.handleError(ErrCodeDBConnectionFailed, "数据库连接测试失败", "", err)
		}

		// 创建数据库（如果不存在）
		_, err = db.Exec(fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbConfig.DBName))
		if err != nil {
			db.Close()
			return dm.handleError(ErrCodeDBConnectionFailed, "创建数据库失败", "", err)
		}

		// 关闭不带数据库名的连接
		db.Close()

		// 重新连接（这次包含数据库名）
		db, err = sql.Open(dbConfig.Driver, dm.getDSN())
		if err != nil {
			return dm.handleError(ErrCodeDBConnectionFailed, "连接到指定数据库失败", "", err)
		}
	}

	// 测试新连接
	if err := db.Ping(); err != nil {
		db.Close()
		return dm.handleError(ErrCodeDBConnectionFailed, "数据库连接测试失败", "", err)
	}

	// 优化数据库连接池配置，提升批量操作性能
	db.SetMaxOpenConns(50)                 // 最大打开连接数
	db.SetMaxIdleConns(10)                 // 最大空闲连接数
	db.SetConnMaxLifetime(5 * time.Minute) // 连接最大生存时间

	dm.db = db
	return nil
}

// getDSN 获取数据库连接字符串
func (dm *DBManager) getDSN() string {
	if dm.config.Driver == "sqlite3" {
		return dm.config.DBName
	}
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dm.config.User, dm.config.Password, dm.config.Host, dm.config.Port, dm.config.DBName)
}

// GetDB 获取数据库连接实例
func (dm *DBManager) GetDB() *sql.DB {
	return dm.db
}

// Close 关闭数据库连接
func (dm *DBManager) Close() error {
	if dm.db != nil {
		return dm.db.Close()
	}
	return nil
}

// handleError 处理错误
func (dm *DBManager) handleError(code ErrorCode, message, url string, cause error) error {
	err := dm.errorManager.NewError(code, message, url, "db_manager", cause)
	dm.errorManager.HandleError(err)
	return err
}
