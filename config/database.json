{"_comment": "WebScan 数据库配置文件 - MySQL数据库连接配置", "_description": "用于存储扫描结果的数据库连接信息", "_security_note": "生产环境请修改默认密码并使用强密码", "driver": "mysql", "_driver_comment": "数据库驱动类型：mysql, postgresql, sqlite3", "_driver_note": "目前主要支持MySQL，其他数据库需要相应的驱动", "host": "127.0.0.1", "_host_comment": "数据库服务器地址：本地使用127.0.0.1，远程使用实际IP地址", "port": 3306, "_port_comment": "数据库端口：MySQL默认3306，PostgreSQL默认5432", "user": "root", "_user_comment": "数据库用户名：建议创建专用用户而非使用root", "_user_security": "生产环境请使用权限受限的专用数据库用户", "password": "Lyf123@@", "_password_comment": "数据库密码：请修改为实际密码", "_password_security": "强烈建议使用复杂密码并定期更换", "dbname": "webscan", "_dbname_comment": "数据库名称：存储扫描结果的数据库，需要预先创建", "_dbname_note": "数据库会自动创建所需的表结构", "sslmode": "disable", "_sslmode_comment": "SSL模式：disable=禁用，require=必需，prefer=优先", "_sslmode_note": "生产环境建议启用SSL加密连接"}