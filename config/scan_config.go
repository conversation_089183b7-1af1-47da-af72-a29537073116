package config

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// DBConfig 数据库配置结构
type DBConfig struct {
	Driver   string `json:"driver"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	DBName   string `json:"dbname"`
	SSLMode  string `json:"sslmode"`
}

// 全局数据库配置
var CurrentDBConfig *DBConfig

// ScanConfig 扫描配置结构
type ScanConfig struct {
	Browser  BrowserConfig  `json:"browser"`
	Scan     ScanSettings   `json:"scan"`
	Memory   MemoryConfig   `json:"memory"`
	Timeout  TimeoutConfig  `json:"timeout"`
	Security SecurityConfig `json:"security"`
}

// BrowserConfig 浏览器配置
type BrowserConfig struct {
	Headless    bool     `json:"headless"`
	UserAgent   string   `json:"user_agent"`
	Viewport    Viewport `json:"viewport"`
	Args        []string `json:"args"`
	ProxyServer string   `json:"proxy_server,omitempty"`
}

// Viewport 视口配置
type Viewport struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

// ScanSettings 扫描设置
type ScanSettings struct {
	MaxDepth         int    `json:"max_depth"`
	MaxConcurrent    int    `json:"max_concurrent"`
	EnableJSInfo     bool   `json:"enable_js_info"`
	MaxRetries       int    `json:"max_retries"`
	BrowserPoolSize  int    `json:"browser_pool_size"`
	PageWaitTime     string `json:"page_wait_time"`     // 页面额外等待时间
	RequestDelay     string `json:"request_delay"`      // 请求间隔时间
	LinkConcurrency  int    `json:"link_concurrency"`   // 子链接并发数（0表示使用max_concurrent/2）
	EnableRetryDelay bool   `json:"enable_retry_delay"` // 是否启用重试延迟
}

// MemoryConfig 内存配置
type MemoryConfig struct {
	MaxResponseCache  int    `json:"max_response_cache"`
	MemoryThresholdMB uint64 `json:"memory_threshold_mb"`
	EnableGCForce     bool   `json:"enable_gc_force"`
	CleanupInterval   string `json:"cleanup_interval"`
}

// TimeoutConfig 超时配置
type TimeoutConfig struct {
	PageLoad       string                `json:"page_load"`
	Request        string                `json:"request"`
	BrowserGet     string                `json:"browser_get"`
	ScanTotal      string                `json:"scan_total"`
	URLProcessing  URLProcessingTimeout  `json:"url_processing"`
}

// URLProcessingTimeout URL处理超时配置
type URLProcessingTimeout struct {
	MaxInactivityTime     string `json:"max_inactivity_time"`     // 最大无活动时间
	ActivityCheckInterval string `json:"activity_check_interval"` // 活跃度检查间隔
	MinProcessingTime     string `json:"min_processing_time"`     // 最小处理时间
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	AllowedIPs     []string `json:"allowed_ips,omitempty"`
	BlockedIPs     []string `json:"blocked_ips,omitempty"`
	AllowedDomains []string `json:"allowed_domains,omitempty"`
	BlockedDomains []string `json:"blocked_domains,omitempty"`
	MaxURLLength   int      `json:"max_url_length"`
}

var (
	// CurrentScanConfig 当前使用的扫描配置
	CurrentScanConfig *ScanConfig
)

// DefaultScanConfig 返回默认配置
func DefaultScanConfig() *ScanConfig {
	return &ScanConfig{
		Browser: BrowserConfig{
			Headless:  true,
			UserAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			Viewport: Viewport{
				Width:  1920,
				Height: 1080,
			},
			Args: []string{
				"--disable-setuid-sandbox",
				"--disable-blink-features=AutomationControlled",
				"--disable-web-security",
				"--no-sandbox",
				"--disable-gpu",
				"--disable-dev-shm-usage",
				"--disable-accelerated-2d-canvas",
				"--no-first-run",
				"--no-zygote",
				"--single-process",
				"--disable-extensions",
			},
		},
		Scan: ScanSettings{
			MaxDepth:         3,
			MaxConcurrent:    4,
			EnableJSInfo:     false,
			MaxRetries:       2,
			BrowserPoolSize:  4,
			PageWaitTime:     "2s",
			RequestDelay:     "100ms",
			LinkConcurrency:  0, // 0表示使用max_concurrent/2
			EnableRetryDelay: true,
		},
		Memory: MemoryConfig{
			MaxResponseCache:  500,
			MemoryThresholdMB: 1024, // 1GB
			EnableGCForce:     true,
			CleanupInterval:   "30s",
		},
		Timeout: TimeoutConfig{
			PageLoad:   "30s",
			Request:    "10s",
			BrowserGet: "30s",
			ScanTotal:  "1h",
			URLProcessing: URLProcessingTimeout{
				MaxInactivityTime:     "60s",
				ActivityCheckInterval: "10s",
				MinProcessingTime:     "30s",
			},
		},
		Security: SecurityConfig{
			BlockedIPs: []string{
				"127.0.0.1",
				"0.0.0.0",
				"***************",
			},
			MaxURLLength: 2048,
		},
	}
}

// LoadScanConfig 从配置文件加载扫描配置
func LoadScanConfig(configPath string) error {
	// 如果未指定配置文件路径，使用默认配置
	if configPath == "" {
		CurrentScanConfig = DefaultScanConfig()
		return nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析JSON配置
	config := DefaultScanConfig() // 从默认配置开始
	if err := json.Unmarshal(data, config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 验证配置
	if err := validateScanConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	CurrentScanConfig = config
	return nil
}

// validateScanConfig 验证配置的有效性
func validateScanConfig(config *ScanConfig) error {
	// 验证扫描设置
	if config.Scan.MaxDepth < 0 {
		return fmt.Errorf("最大深度不能为负数")
	}
	if config.Scan.MaxConcurrent <= 0 {
		return fmt.Errorf("并发数必须大于0")
	}
	if config.Scan.BrowserPoolSize <= 0 {
		return fmt.Errorf("浏览器池大小必须大于0")
	}

	// 验证新增的扫描设置
	if config.Scan.PageWaitTime != "" {
		if _, err := time.ParseDuration(config.Scan.PageWaitTime); err != nil {
			return fmt.Errorf("无效的页面等待时间配置: %s", config.Scan.PageWaitTime)
		}
	}
	if config.Scan.RequestDelay != "" {
		if _, err := time.ParseDuration(config.Scan.RequestDelay); err != nil {
			return fmt.Errorf("无效的请求延迟配置: %s", config.Scan.RequestDelay)
		}
	}
	if config.Scan.LinkConcurrency < 0 {
		return fmt.Errorf("子链接并发数不能为负数")
	}

	// 验证内存设置
	if config.Memory.MaxResponseCache <= 0 {
		return fmt.Errorf("响应缓存大小必须大于0")
	}
	if config.Memory.MemoryThresholdMB <= 0 {
		return fmt.Errorf("内存阈值必须大于0")
	}

	// 验证超时设置
	timeouts := map[string]string{
		"page_load":   config.Timeout.PageLoad,
		"request":     config.Timeout.Request,
		"browser_get": config.Timeout.BrowserGet,
		"scan_total":  config.Timeout.ScanTotal,
	}

	for name, timeout := range timeouts {
		if _, err := time.ParseDuration(timeout); err != nil {
			return fmt.Errorf("无效的超时配置 %s: %s", name, timeout)
		}
	}

	// 验证URL处理超时设置
	urlProcessingTimeouts := map[string]string{
		"max_inactivity_time":     config.Timeout.URLProcessing.MaxInactivityTime,
		"activity_check_interval": config.Timeout.URLProcessing.ActivityCheckInterval,
		"min_processing_time":     config.Timeout.URLProcessing.MinProcessingTime,
	}

	for name, timeout := range urlProcessingTimeouts {
		if timeout != "" {
			if _, err := time.ParseDuration(timeout); err != nil {
				return fmt.Errorf("无效的URL处理超时配置 %s: %s", name, timeout)
			}
		}
	}

	// 验证视口设置
	if config.Browser.Viewport.Width <= 0 || config.Browser.Viewport.Height <= 0 {
		return fmt.Errorf("视口尺寸必须大于0")
	}

	return nil
}

// SaveScanConfig 保存配置到文件
func SaveScanConfig(configPath string, config *ScanConfig) error {
	// 验证配置
	if err := validateScanConfig(config); err != nil {
		return err
	}

	// 序列化配置
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

// GetPageLoadTimeout 获取页面加载超时时间
func (c *ScanConfig) GetPageLoadTimeout() time.Duration {
	duration, _ := time.ParseDuration(c.Timeout.PageLoad)
	return duration
}

// GetRequestTimeout 获取请求超时时间
func (c *ScanConfig) GetRequestTimeout() time.Duration {
	duration, _ := time.ParseDuration(c.Timeout.Request)
	return duration
}

// GetBrowserGetTimeout 获取浏览器获取超时时间
func (c *ScanConfig) GetBrowserGetTimeout() time.Duration {
	duration, _ := time.ParseDuration(c.Timeout.BrowserGet)
	return duration
}

// GetScanTotalTimeout 获取总扫描超时时间
func (c *ScanConfig) GetScanTotalTimeout() time.Duration {
	duration, _ := time.ParseDuration(c.Timeout.ScanTotal)
	return duration
}

// GetCleanupInterval 获取清理间隔时间
func (c *ScanConfig) GetCleanupInterval() time.Duration {
	duration, _ := time.ParseDuration(c.Memory.CleanupInterval)
	return duration
}

// GetMaxInactivityTime 获取最大无活动时间
func (c *ScanConfig) GetMaxInactivityTime() time.Duration {
	if c.Timeout.URLProcessing.MaxInactivityTime == "" {
		return 60 * time.Second // 默认60秒
	}
	duration, err := time.ParseDuration(c.Timeout.URLProcessing.MaxInactivityTime)
	if err != nil {
		return 60 * time.Second // 解析失败时使用默认值
	}
	return duration
}



// GetActivityCheckInterval 获取活跃度检查间隔
func (c *ScanConfig) GetActivityCheckInterval() time.Duration {
	if c.Timeout.URLProcessing.ActivityCheckInterval == "" {
		return 10 * time.Second // 默认10秒
	}
	duration, err := time.ParseDuration(c.Timeout.URLProcessing.ActivityCheckInterval)
	if err != nil {
		return 10 * time.Second // 解析失败时使用默认值
	}
	return duration
}

// GetMinProcessingTime 获取最小处理时间
func (c *ScanConfig) GetMinProcessingTime() time.Duration {
	if c.Timeout.URLProcessing.MinProcessingTime == "" {
		return 30 * time.Second // 默认30秒
	}
	duration, err := time.ParseDuration(c.Timeout.URLProcessing.MinProcessingTime)
	if err != nil {
		return 30 * time.Second // 解析失败时使用默认值
	}
	return duration
}

// GetPageWaitTime 获取页面额外等待时间
func (c *ScanConfig) GetPageWaitTime() time.Duration {
	if c.Scan.PageWaitTime == "" {
		return 2 * time.Second // 默认2秒
	}
	duration, err := time.ParseDuration(c.Scan.PageWaitTime)
	if err != nil {
		return 2 * time.Second // 解析失败时使用默认值
	}
	return duration
}

// GetRequestDelay 获取请求间隔时间
func (c *ScanConfig) GetRequestDelay() time.Duration {
	if c.Scan.RequestDelay == "" {
		return 100 * time.Millisecond // 默认100毫秒
	}
	duration, err := time.ParseDuration(c.Scan.RequestDelay)
	if err != nil {
		return 100 * time.Millisecond // 解析失败时使用默认值
	}
	return duration
}

// GetLinkConcurrency 获取子链接并发数
func (c *ScanConfig) GetLinkConcurrency() int {
	if c.Scan.LinkConcurrency <= 0 {
		// 如果未设置或为0，使用主并发数的一半
		linkConcurrent := c.Scan.MaxConcurrent / 2
		if linkConcurrent < 1 {
			linkConcurrent = 1
		}
		return linkConcurrent
	}
	return c.Scan.LinkConcurrency
}

// IsIPAllowed 检查IP是否被允许
func (c *ScanConfig) IsIPAllowed(ip string) bool {
	// 检查黑名单
	for _, blockedIP := range c.Security.BlockedIPs {
		if ip == blockedIP {
			return false
		}
	}

	// 如果有白名单，检查白名单
	if len(c.Security.AllowedIPs) > 0 {
		for _, allowedIP := range c.Security.AllowedIPs {
			if ip == allowedIP {
				return true
			}
		}
		return false // 有白名单但不在其中
	}

	return true // 没有白名单且不在黑名单中
}

// LoadDBConfig 加载数据库配置
func LoadDBConfig(configPath string) error {
	if configPath == "" {
		return fmt.Errorf("数据库配置文件路径不能为空")
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取数据库配置文件失败: %w", err)
	}

	var dbConfig DBConfig
	if err := json.Unmarshal(data, &dbConfig); err != nil {
		return fmt.Errorf("解析数据库配置文件失败: %w", err)
	}

	CurrentDBConfig = &dbConfig
	return nil
}

// GetDSN 获取数据库连接字符串
func (db *DBConfig) GetDSN() string {
	if db.Driver == "sqlite3" {
		return db.DBName
	}
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		db.User, db.Password, db.Host, db.Port, db.DBName)
}

// IsDomainAllowed 检查域名是否被允许
func (c *ScanConfig) IsDomainAllowed(domain string) bool {
	// 检查黑名单
	for _, blockedDomain := range c.Security.BlockedDomains {
		if domain == blockedDomain {
			return false
		}
	}

	// 如果有白名单，检查白名单
	if len(c.Security.AllowedDomains) > 0 {
		for _, allowedDomain := range c.Security.AllowedDomains {
			if domain == allowedDomain {
				return true
			}
		}
		return false // 有白名单但不在其中
	}

	return true // 没有白名单且不在黑名单中
}
