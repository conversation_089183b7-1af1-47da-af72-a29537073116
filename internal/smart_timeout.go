package internal

import (
	"fmt"
	"sync/atomic"
	"time"

	"web_scanner/config"
)

// URLProcessingTracker URL处理跟踪器
type URLProcessingTracker struct {
	URL              string    // 正在处理的URL
	StartTime        time.Time // 开始处理时间
	LastActivityTime time.Time // 最后活动时间
	ActiveRequests   int32     // 活跃请求数量（使用原子操作）
	TotalRequests    int32     // 总请求数量
	CompletedRequests int32    // 已完成请求数量
	ActiveSubScans   int32     // 活跃子链接扫描数量
	IsCompleted      bool      // 是否已完成处理
}

// SmartTimeoutManager 智能超时管理器
type SmartTimeoutManager struct {
	config *config.ScanConfig
}

// NewSmartTimeoutManager 创建智能超时管理器
func NewSmartTimeoutManager(cfg *config.ScanConfig) *SmartTimeoutManager {
	return &SmartTimeoutManager{
		config: cfg,
	}
}

// NewURLProcessingTracker 创建URL处理跟踪器
func NewURLProcessingTracker(url string) *URLProcessingTracker {
	now := time.Now()
	return &URLProcessingTracker{
		URL:              url,
		StartTime:        now,
		LastActivityTime: now,
		ActiveRequests:   0,
		TotalRequests:    0,
		CompletedRequests: 0,
		ActiveSubScans:   0,
		IsCompleted:      false,
	}
}

// AddActiveRequest 增加活跃请求计数
func (t *URLProcessingTracker) AddActiveRequest() {
	atomic.AddInt32(&t.ActiveRequests, 1)
	atomic.AddInt32(&t.TotalRequests, 1)
	t.LastActivityTime = time.Now()
}

// CompleteRequest 完成一个请求
func (t *URLProcessingTracker) CompleteRequest() {
	atomic.AddInt32(&t.ActiveRequests, -1)
	atomic.AddInt32(&t.CompletedRequests, 1)
	t.LastActivityTime = time.Now()
}

// GetActiveRequestCount 获取当前活跃请求数量
func (t *URLProcessingTracker) GetActiveRequestCount() int32 {
	return atomic.LoadInt32(&t.ActiveRequests)
}

// GetTotalRequestCount 获取总请求数量
func (t *URLProcessingTracker) GetTotalRequestCount() int32 {
	return atomic.LoadInt32(&t.TotalRequests)
}

// GetCompletedRequestCount 获取已完成请求数量
func (t *URLProcessingTracker) GetCompletedRequestCount() int32 {
	return atomic.LoadInt32(&t.CompletedRequests)
}

// AddActiveSubScan 增加活跃子链接扫描计数
func (t *URLProcessingTracker) AddActiveSubScan() {
	atomic.AddInt32(&t.ActiveSubScans, 1)
	t.LastActivityTime = time.Now()
}

// CompleteSubScan 完成一个子链接扫描
func (t *URLProcessingTracker) CompleteSubScan() {
	atomic.AddInt32(&t.ActiveSubScans, -1)
	t.LastActivityTime = time.Now()
}

// GetActiveSubScanCount 获取当前活跃子链接扫描数量
func (t *URLProcessingTracker) GetActiveSubScanCount() int32 {
	return atomic.LoadInt32(&t.ActiveSubScans)
}

// UpdateActivity 更新活动时间（用于非请求相关的活动）
func (t *URLProcessingTracker) UpdateActivity() {
	t.LastActivityTime = time.Now()
}

// MarkCompleted 标记处理完成
func (t *URLProcessingTracker) MarkCompleted() {
	t.IsCompleted = true
	t.LastActivityTime = time.Now()
}

// ShouldTimeout 检查是否应该超时
// 返回值：(是否超时, 超时原因)
func (m *SmartTimeoutManager) ShouldTimeout(tracker *URLProcessingTracker) (bool, string) {
	now := time.Now()
	totalTime := now.Sub(tracker.StartTime)
	inactivityTime := now.Sub(tracker.LastActivityTime)

	// 获取配置的超时参数
	maxInactivityTime := m.config.GetMaxInactivityTime()
	minProcessingTime := m.config.GetMinProcessingTime()

	// 1. 最小处理时间保护 - 避免过早判断
	if totalTime < minProcessingTime {
		return false, ""
	}

	// 2. 如果已标记为完成，不超时
	if tracker.IsCompleted {
		return false, ""
	}

	// 3. 检查是否有活跃请求或子链接扫描
	activeRequests := tracker.GetActiveRequestCount()
	activeSubScans := tracker.GetActiveSubScanCount()
	if activeRequests > 0 || activeSubScans > 0 {
		// 有活跃请求或子链接扫描时，永不超时，只要有活动就继续
		return false, ""
	}

	// 4. 没有活跃请求时，检查无活动时间
	if inactivityTime > maxInactivityTime {
		return true, fmt.Sprintf("无活动超时: %v (总处理时间: %v)", inactivityTime, totalTime)
	}

	// 5. 如果没有活跃请求且在无活动时间内，继续等待
	return false, ""
}

// GetProcessingStatus 获取处理状态信息
func (m *SmartTimeoutManager) GetProcessingStatus(tracker *URLProcessingTracker) ProcessingStatus {
	now := time.Now()
	totalTime := now.Sub(tracker.StartTime)
	inactivityTime := now.Sub(tracker.LastActivityTime)

	return ProcessingStatus{
		URL:               tracker.URL,
		TotalTime:         totalTime,
		InactivityTime:    inactivityTime,
		ActiveRequests:    tracker.GetActiveRequestCount(),
		TotalRequests:     tracker.GetTotalRequestCount(),
		CompletedRequests: tracker.GetCompletedRequestCount(),
		ActiveSubScans:    tracker.GetActiveSubScanCount(),
		IsCompleted:       tracker.IsCompleted,
	}
}

// ProcessingStatus 处理状态信息
type ProcessingStatus struct {
	URL               string        // URL
	TotalTime         time.Duration // 总处理时间
	InactivityTime    time.Duration // 无活动时间
	ActiveRequests    int32         // 活跃请求数
	TotalRequests     int32         // 总请求数
	CompletedRequests int32         // 已完成请求数
	ActiveSubScans    int32         // 活跃子链接扫描数
	IsCompleted       bool          // 是否完成
}

// String 返回状态的字符串表示
func (s ProcessingStatus) String() string {
	return fmt.Sprintf("URL: %s, 总时间: %v, 无活动时间: %v, 活跃请求: %d/%d, 活跃子链接: %d, 已完成: %t",
		s.URL, s.TotalTime, s.InactivityTime, s.ActiveRequests, s.TotalRequests, s.ActiveSubScans, s.IsCompleted)
}

// GetProgressPercentage 获取进度百分比
func (s ProcessingStatus) GetProgressPercentage() float64 {
	if s.TotalRequests == 0 {
		return 0.0
	}
	return float64(s.CompletedRequests) / float64(s.TotalRequests) * 100.0
}

// IsActive 检查是否处于活跃状态
func (s ProcessingStatus) IsActive() bool {
	return s.ActiveRequests > 0 || s.ActiveSubScans > 0 || !s.IsCompleted
}
