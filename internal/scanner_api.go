package internal

import (
	"strings"

	"github.com/playwright-community/playwright-go"
)

// ClassifyAPI 用于判断请求是否为API（如XHR、fetch、websocket等）
// 严格版本：只识别真正的动态请求，避免误判普通页面
func ClassifyAPI(req playwright.Request) bool {
	u := strings.ToLower(req.URL())
	rt := req.ResourceType()
	method := req.Method()

	// 0. 首先排除明显的静态资源
	if isStaticResource(u) {
		return false
	}

	// 1. 最高优先级：明确的API请求类型（资源类型）
	if rt == "xhr" || rt == "fetch" || rt == "websocket" || rt == "eventsource" {
		return true
	}

	// 2. 高优先级：非GET请求方法（通常是API调用）
	if method == "POST" || method == "PUT" || method == "PATCH" || method == "DELETE" || method == "OPTIONS" {
		return true
	}

	// 3. 中等优先级：明确的API路径模式（更严格的匹配）
	strictAPIPatterns := []string{
		"/api/", "/ajax/", "/rest/", "/graphql", "/rpc/",
		"/service/", "/endpoint/", "/data/", "/query/",
		"/v1/", "/v2/", "/v3/", "/v4/", "/v5/",
		"/webhook/", "/callback/",
	}

	for _, pattern := range strictAPIPatterns {
		if strings.Contains(u, pattern) {
			return true
		}
	}

	// 4. 低优先级：特殊的API文件扩展名
	apiExtensions := []string{".json", ".xml", ".do", ".action", ".ashx", ".asmx"}
	for _, ext := range apiExtensions {
		if strings.HasSuffix(u, ext) {
			return true
		}
	}

	// 5. 最低优先级：动态参数，但排除明显的页面请求
	if strings.Contains(u, "?") && (strings.Contains(u, "=") || strings.Contains(u, "&")) {
		// 排除明显的页面文件
		pageExtensions := []string{".html", ".htm", ".php", ".jsp", ".asp", ".aspx"}
		for _, ext := range pageExtensions {
			if strings.HasSuffix(u, ext) {
				return false
			}
		}

		// 排除明显的页面路径
		pagePatterns := []string{"/login.html", "/register.html", "/index.html", "/home.html"}
		for _, pattern := range pagePatterns {
			if strings.Contains(u, pattern) {
				return false
			}
		}

		return true
	}

	return false
}

// isStaticResource 判断是否为静态资源
func isStaticResource(url string) bool {
	staticExtensions := []string{
		".js", ".css", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg",
		".woff", ".woff2", ".ttf", ".eot", ".map", ".pdf", ".doc", ".docx",
		".xls", ".xlsx", ".zip", ".rar", ".tar", ".gz", ".mp4", ".mp3",
		".avi", ".mov", ".wmv", ".flv", ".webm", ".ogg", ".wav",
	}

	lowerURL := strings.ToLower(url)

	// 检查文件扩展名
	for _, ext := range staticExtensions {
		if strings.HasSuffix(lowerURL, ext) {
			return true
		}
	}

	// 检查URL路径中的静态资源目录
	staticPaths := []string{
		"/static/", "/assets/", "/images/", "/img/", "/css/", "/js/",
		"/fonts/", "/media/", "/resources/", "/dist/", "/build/",
		"/styles/", "/scripts/", "/public/", "/libs/", "/vendors/",
	}
	for _, path := range staticPaths {
		if strings.Contains(lowerURL, path) {
			return true
		}
	}
	return false
}
