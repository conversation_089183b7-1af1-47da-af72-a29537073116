package internal

import (
	"container/list"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/playwright-community/playwright-go"
)

// LRUItem LRU缓存项
type LRUItem struct {
	Key      string              // 缓存键
	Value    playwright.Response // 响应对象
	Size     int64               // 数据大小（字节）
	LastUsed time.Time           // 最后使用时间
}

// ResponseManager 响应内容管理器，实现LRU缓存机制
type ResponseManager struct {
	cache       map[string]*list.Element // 缓存映射表，值为链表元素
	lruList     *list.List               // LRU双向链表
	mu          sync.RWMutex             // 读写锁，保证并发安全
	maxSize     int                      // 最大缓存条目数
	maxMemory   int64                    // 最大内存使用量（字节）
	currentSize int64                    // 当前内存使用量
	cleanupCh   chan string              // 清理通道
	stopCh      chan bool                // 停止信号
	hitCount    int64                    // 缓存命中次数
	missCount   int64                    // 缓存未命中次数
}

// NewResponseManager 创建新的响应管理器
func NewResponseManager(maxSize int) *ResponseManager {
	if maxSize <= 0 {
		maxSize = 1000 // 默认最大1000个响应
	}

	rm := &ResponseManager{
		cache:     make(map[string]*list.Element),
		lruList:   list.New(),
		maxSize:   maxSize,
		maxMemory: 100 * 1024 * 1024, // 默认最大100MB内存
		cleanupCh: make(chan string, maxSize),
		stopCh:    make(chan bool),
	}

	// 启动清理goroutine
	go rm.cleanupWorker()

	return rm
}

// NewResponseManagerWithMemoryLimit 创建带内存限制的响应管理器
func NewResponseManagerWithMemoryLimit(maxSize int, maxMemoryMB int64) *ResponseManager {
	rm := NewResponseManager(maxSize)
	rm.maxMemory = maxMemoryMB * 1024 * 1024 // 转换为字节
	return rm
}

// Store 存储响应到LRU缓存
func (rm *ResponseManager) Store(url string, response playwright.Response) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 估算响应大小（简化计算）
	responseSize := rm.estimateResponseSize(response)

	// 如果已存在，更新位置
	if elem, exists := rm.cache[url]; exists {
		// 更新现有项
		item := elem.Value.(*LRUItem)
		rm.currentSize = rm.currentSize - item.Size + responseSize
		item.Value = response
		item.Size = responseSize
		item.LastUsed = time.Now()
		rm.lruList.MoveToFront(elem)
		rm.hitCount++
		return
	}

	// 检查是否需要清理空间
	for (len(rm.cache) >= rm.maxSize || rm.currentSize+responseSize > rm.maxMemory) && rm.lruList.Len() > 0 {
		rm.evictLRU()
	}

	// 添加新项
	item := &LRUItem{
		Key:      url,
		Value:    response,
		Size:     responseSize,
		LastUsed: time.Now(),
	}

	elem := rm.lruList.PushFront(item)
	rm.cache[url] = elem
	rm.currentSize += responseSize
	rm.missCount++
}

// Get 获取响应
func (rm *ResponseManager) Get(url string) (playwright.Response, bool) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	elem, exists := rm.cache[url]
	if !exists {
		rm.missCount++
		return nil, false
	}

	// 移动到链表头部（最近使用）
	item := elem.Value.(*LRUItem)
	item.LastUsed = time.Now()
	rm.lruList.MoveToFront(elem)
	rm.hitCount++

	return item.Value, true
}

// Delete 删除响应
func (rm *ResponseManager) Delete(url string) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if elem, exists := rm.cache[url]; exists {
		item := elem.Value.(*LRUItem)
		rm.currentSize -= item.Size
		rm.lruList.Remove(elem)
		delete(rm.cache, url)
	}
}

// evictLRU 淘汰最久未使用的缓存项（需要在锁内调用）
func (rm *ResponseManager) evictLRU() {
	if rm.lruList.Len() == 0 {
		return
	}

	// 获取最久未使用的元素
	elem := rm.lruList.Back()
	if elem != nil {
		item := elem.Value.(*LRUItem)
		rm.currentSize -= item.Size
		rm.lruList.Remove(elem)
		delete(rm.cache, item.Key)
	}
}

// estimateResponseSize 估算响应大小
func (rm *ResponseManager) estimateResponseSize(response playwright.Response) int64 {
	// 简化的大小估算，实际应用中可以更精确
	// 这里假设每个响应平均占用1KB
	return 1024
}

// triggerCleanup 触发清理（需要在锁内调用）
func (rm *ResponseManager) triggerCleanup() {
	// 使用LRU策略清理一半的缓存
	target := len(rm.cache) / 2
	for i := 0; i < target && rm.lruList.Len() > 0; i++ {
		rm.evictLRU()
	}

	// 强制垃圾回收
	runtime.GC()
}

// cleanupWorker 清理工作协程
func (rm *ResponseManager) cleanupWorker() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rm.periodicCleanup()
		case url := <-rm.cleanupCh:
			rm.Delete(url)
		case <-rm.stopCh:
			return
		}
	}
}

// periodicCleanup 定期清理
func (rm *ResponseManager) periodicCleanup() {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 如果缓存数量或内存使用超过阈值，进行清理
	if len(rm.cache) > rm.maxSize*3/4 || rm.currentSize > rm.maxMemory*3/4 {
		rm.triggerCleanup()
	}
}

// Close 关闭响应管理器
func (rm *ResponseManager) Close() {
	close(rm.stopCh)
	close(rm.cleanupCh)

	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 清空缓存
	rm.cache = make(map[string]*list.Element)
	rm.lruList = list.New()
	rm.currentSize = 0
	runtime.GC()
}

// Size 返回当前存储的响应数量
func (rm *ResponseManager) Size() int {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	return len(rm.cache)
}

// MemoryUsage 获取当前内存使用量
func (rm *ResponseManager) MemoryUsage() int64 {
	rm.mu.RLock()
	defer rm.mu.RUnlock()
	return rm.currentSize
}

// GetCacheStats 获取缓存统计信息
func (rm *ResponseManager) GetCacheStats() map[string]interface{} {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	hitRate := float64(0)
	total := rm.hitCount + rm.missCount
	if total > 0 {
		hitRate = float64(rm.hitCount) / float64(total) * 100
	}

	return map[string]interface{}{
		"cache_size":      len(rm.cache),
		"max_size":        rm.maxSize,
		"memory_usage":    rm.currentSize,
		"max_memory":      rm.maxMemory,
		"hit_count":       rm.hitCount,
		"miss_count":      rm.missCount,
		"hit_rate":        fmt.Sprintf("%.2f%%", hitRate),
		"memory_usage_mb": float64(rm.currentSize) / (1024 * 1024),
		"max_memory_mb":   float64(rm.maxMemory) / (1024 * 1024),
	}
}

// MemoryStats 内存统计信息
type MemoryStats struct {
	Alloc         uint64 // 当前分配的内存
	TotalAlloc    uint64 // 总分配的内存
	Sys           uint64 // 系统内存
	NumGC         uint32 // GC次数
	ResponseCount int    // 响应数量
}

// GetMemoryStats 获取内存统计信息
func (rm *ResponseManager) GetMemoryStats() MemoryStats {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return MemoryStats{
		Alloc:         m.Alloc,
		TotalAlloc:    m.TotalAlloc,
		Sys:           m.Sys,
		NumGC:         m.NumGC,
		ResponseCount: rm.Size(),
	}
}

// ForceGC 强制垃圾回收
func ForceGC() {
	runtime.GC()
	runtime.GC() // 调用两次确保彻底清理
}

// URLSet 线程安全的URL集合，用于去重
type URLSet struct {
	urls sync.Map
}

// NewURLSet 创建新的URL集合
func NewURLSet() *URLSet {
	return &URLSet{}
}

// Add 添加URL
func (s *URLSet) Add(url string) {
	s.urls.Store(url, true)
}

// Contains 检查URL是否存在
func (s *URLSet) Contains(url string) bool {
	_, exists := s.urls.Load(url)
	return exists
}

// ToSlice 转换为切片
func (s *URLSet) ToSlice() []string {
	var result []string
	s.urls.Range(func(key, value interface{}) bool {
		if url, ok := key.(string); ok {
			result = append(result, url)
		}
		return true
	})
	return result
}

// Size 返回集合大小
func (s *URLSet) Size() int {
	count := 0
	s.urls.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// Clear 清空集合
func (s *URLSet) Clear() {
	s.urls = sync.Map{}
}

// MemoryMonitor 内存监控器
type MemoryMonitor struct {
	threshold uint64 // 内存阈值（字节）
	callback  func() // 超过阈值时的回调函数
	stopCh    chan bool
}

// NewMemoryMonitor 创建内存监控器
func NewMemoryMonitor(thresholdMB uint64, callback func()) *MemoryMonitor {
	return &MemoryMonitor{
		threshold: thresholdMB * 1024 * 1024, // 转换为字节
		callback:  callback,
		stopCh:    make(chan bool),
	}
}

// Start 开始监控
func (m *MemoryMonitor) Start() {
	go func() {
		ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				var stats runtime.MemStats
				runtime.ReadMemStats(&stats)

				if stats.Alloc > m.threshold {
					if m.callback != nil {
						m.callback()
					}
				}
			case <-m.stopCh:
				return
			}
		}
	}()
}

// Stop 停止监控
func (m *MemoryMonitor) Stop() {
	close(m.stopCh)
}
