package internal

import "strings"

// Page 或 Frame 都可以传入
func ExtractDOMResources(evalTarget interface {
	Evaluate(expression string, arg ...interface{}) (interface{}, error)
}) ([]string, error) {
	script := `
	() => {
		const urls = [];
		const push = (attr) => el => {
			const url = el[attr];
			if (url && !url.startsWith('javascript:') && !url.startsWith('data:') && !url.startsWith('#')) {
				urls.push(url);
			}
		};

		document.querySelectorAll('script[src]').forEach(push('src'));
		document.querySelectorAll('link[rel=stylesheet]').forEach(push('href'));
		document.querySelectorAll('img[src]').forEach(push('src'));
		document.querySelectorAll('a[href]').forEach(push('href'));

		return urls;
	}`
	result, err := evalTarget.Evaluate(script)
	if err != nil {
		return nil, err
	}
	if arr, ok := result.([]interface{}); ok {
		var list []string
		for _, val := range arr {
			if str, ok := val.(string); ok {
				// 服务器端再次验证，确保过滤掉无效URL
				if isValidResourceURL(str) {
					list = append(list, str)
				}
			}
		}
		return list, nil
	}
	return nil, nil
}

// isValidResourceURL 验证资源URL是否有效
func isValidResourceURL(url string) bool {
	// 过滤掉JavaScript伪协议、data协议、锚点等
	if strings.HasPrefix(url, "javascript:") ||
		strings.HasPrefix(url, "data:") ||
		strings.HasPrefix(url, "#") ||
		strings.HasPrefix(url, "mailto:") ||
		strings.HasPrefix(url, "tel:") ||
		strings.HasPrefix(url, "sms:") {
		return false
	}

	// 必须是http/https协议或相对路径
	if !strings.HasPrefix(url, "http://") &&
		!strings.HasPrefix(url, "https://") &&
		!strings.HasPrefix(url, "/") &&
		!strings.HasPrefix(url, "./") &&
		!strings.HasPrefix(url, "../") {
		return false
	}

	// 长度检查
	if len(url) < 1 || len(url) > 2048 {
		return false
	}

	return true
}
