package internal

import (
	"fmt"
	"sync"
	"time"

	"web_scanner/config"

	"github.com/playwright-community/playwright-go"
)

// BrowserPool 浏览器实例池
type BrowserPool struct {
	sessions    chan *ScanSession
	maxSize     int
	currentSize int
	mu          sync.Mutex
	pw          *playwright.Playwright
	browser     playwright.Browser
	closed      bool
}

// NewBrowserPool 创建新的浏览器实例池
func NewBrowserPool(size int) (*BrowserPool, error) {
	if size <= 0 {
		size = 4 // 默认大小
	}

	// 启动Playwright
	pw, err := playwright.Run()
	if err != nil {
		return nil, fmt.Errorf("Playwright 启动失败: %w", err)
	}

	// 使用配置文件中的浏览器设置
	browserConfig := config.CurrentScanConfig.Browser

	// 准备浏览器启动选项
	launchOptions := playwright.BrowserTypeLaunchOptions{
		Headless: playwright.<PERSON><PERSON>(browserConfig.Headless),
		Args:     browserConfig.Args,
	}

	// 如果配置了代理，添加代理设置
	if browserConfig.ProxyServer != "" {
		launchOptions.Proxy = &playwright.Proxy{
			Server: browserConfig.ProxyServer,
		}
	}

	// 启动浏览器
	browser, err := pw.Chromium.Launch(launchOptions)
	if err != nil {
		pw.Stop()
		return nil, fmt.Errorf("浏览器启动失败: %w", err)
	}

	pool := &BrowserPool{
		sessions: make(chan *ScanSession, size),
		maxSize:  size,
		pw:       pw,
		browser:  browser,
	}

	// 预创建一些浏览器上下文
	for i := 0; i < size; i++ {
		session, err := pool.createSession()
		if err != nil {
			pool.Close()
			return nil, fmt.Errorf("创建浏览器会话失败: %w", err)
		}
		pool.sessions <- session
		pool.currentSize++
	}

	return pool, nil
}

// createSession 创建新的扫描会话 - 修复代理配置问题
func (p *BrowserPool) createSession() (*ScanSession, error) {
	// 使用配置文件中的浏览器设置
	browserConfig := config.CurrentScanConfig.Browser

	// 准备上下文选项
	contextOptions := playwright.BrowserNewContextOptions{
		UserAgent: playwright.String(browserConfig.UserAgent),
		Viewport: &playwright.Size{
			Width:  browserConfig.Viewport.Width,
			Height: browserConfig.Viewport.Height,
		},
		JavaScriptEnabled: playwright.Bool(true),
		HasTouch:          playwright.Bool(false),
		IsMobile:          playwright.Bool(false),
		Locale:            playwright.String("zh-CN"),
		TimezoneId:        playwright.String("Asia/Shanghai"),
		IgnoreHttpsErrors: playwright.Bool(true),
	}

	// 如果配置了代理，也要在上下文中设置代理（修复代理配置问题）
	if browserConfig.ProxyServer != "" {
		contextOptions.Proxy = &playwright.Proxy{
			Server: browserConfig.ProxyServer,
		}
	}

	context, err := p.browser.NewContext(contextOptions)
	if err != nil {
		return nil, err
	}

	return &ScanSession{
		pw:      p.pw,
		browser: p.browser,
		context: context,
		pool:    p, // 添加对池的引用
	}, nil
}

// Get 从池中获取一个浏览器会话
func (p *BrowserPool) Get() (*ScanSession, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return nil, fmt.Errorf("浏览器池已关闭")
	}

	select {
	case session := <-p.sessions:
		return session, nil
	case <-time.After(30 * time.Second):
		return nil, fmt.Errorf("获取浏览器会话超时")
	}
}

// Put 将浏览器会话归还到池中
func (p *BrowserPool) Put(session *ScanSession) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		session.CloseContext() // 只关闭上下文，不关闭浏览器
		return
	}

	// 检查会话状态
	if session.context == nil {
		// 会话上下文无效，直接丢弃
		return
	}

	select {
	case p.sessions <- session:
		// 成功归还，不输出日志减少噪音
	default:
		// 池已满，不关闭会话，让它自然过期
		// 不调用 session.CloseContext()，让会话自然过期
	}
}

// Close 关闭浏览器池
func (p *BrowserPool) Close() {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return
	}
	p.closed = true

	// 关闭所有会话
	close(p.sessions)
	for session := range p.sessions {
		session.CloseContext()
	}

	// 关闭浏览器和Playwright
	if p.browser != nil {
		p.browser.Close()
	}
	if p.pw != nil {
		p.pw.Stop()
	}
}

// Size 返回池的大小信息
func (p *BrowserPool) Size() (current, max int) {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.currentSize, p.maxSize
}
